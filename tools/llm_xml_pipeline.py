#!/usr/bin/env python3
"""
🤖 LLM XML Pipeline - Универсальный инструмент для исправления сломанных XML файлов

📋 КАК ПОЛЬЗОВАТЬСЯ:
1. Измен<PERSON> SOURCE_PATH ниже на путь к проблемному файлу
2. Запусти: python3 tools/llm_xml_pipeline.py
3. Pipeline создаст промпт в tmp/llm_XXXXX_prompt.json
4. Отправь содержимое промпта в LLM (ChatGPT/Claude)
5. Получи JSON ответ с bash командами для исправления
6. Примени bash команды к файлу tmp/llm_XXXXX_file.fb2
7. Проверь результат: python tools/run_diagnostic_tool.py --input исправленный_файл
8. При успехе замени оригинал с сохранением времени: touch -r оригинал исправленный

📁 ПОДДЕРЖИВАЕМЫЕ ФОРМАТЫ:
- Файлы в архивах: "/path/to/archive.zip::file.fb2"
- Обычные файлы: "/path/to/file.fb2"

🔧 ЭТАПЫ РАБОТЫ:
1. Pipeline копирует проблемный файл в tmp/llm_XXXXX_file.fb2
2. Анализирует XML ошибки и создает промпт tmp/llm_XXXXX_prompt.json
3. ТЫ отправляешь промпт в LLM и получаешь JSON с bash командами
4. ТЫ применяешь bash команды к файлу tmp/llm_XXXXX_file.fb2
5. ТЫ проверяешь результат: python tools/run_diagnostic_tool.py --input файл
6. ТЫ заменяешь оригинал: touch -r оригинал исправленный

⚠️ ВАЖНО: Pipeline только анализирует, исправления делаешь ТЫ bash командами!
"""

import hashlib
import json
import re
import shutil
import xml.etree.ElementTree as ET
import zipfile
from pathlib import Path

# 🎯 НАСТРОЙКИ - ИЗМЕНИ ЗДЕСЬ ПУТЬ К ПРОБЛЕМНОМУ ФАЙЛУ
SOURCE_PATH = "/mnt/storage/books/zip/zip_searchfloor/9000.zip::8357.fb2"

# 📝 ПРИМЕР JSON ОТВЕТА ОТ LLM (для справки):
EXAMPLE_LLM_JSON = """
{
  "bash_commands": [
    "sed -i '37s|.*|<p><emphasis>исправленная строка</emphasis></p>|' tmp/llm_XXXXX_file.fb2",
    "sed -i '38s|.*|<p>другая исправленная строка</p>|' tmp/llm_XXXXX_file.fb2"
  ],
  "explanation": "краткое объяснение что было исправлено",
  "validation": "После применения команд запустите: python tools/run_diagnostic_tool.py --input tmp/llm_XXXXX_file.fb2"
}
"""

# 🔧 BASH КОМАНДЫ ДЛЯ ИСПРАВЛЕНИЯ XML:
# sed -i 'Ns|.*|новое_содержимое|' файл    - заменить строку N
# sed -i 'Na\новая_строка' файл           - вставить после строки N
# sed -i 'Nd' файл                        - удалить строку N
# touch -r оригинал исправленный          - сохранить время файла

# 📁 ЗАМЕНА ОРИГИНАЛА С СОХРАНЕНИЕМ ДАТЫ:
# Для файлов в архивах:
#   1. Извлечь: unzip -j архив.zip файл.fb2 -d tmp/
#   2. Исправить временный файл bash командами
#   3. Заменить в архиве: zip -u архив.zip -j tmp/файл.fb2
#   4. Восстановить дату архива: touch -r backup.zip архив.zip
# Для обычных файлов:
#   1. Создать backup: cp оригинал оригинал.backup
#   2. Исправить временный файл bash командами
#   3. Заменить: cp tmp/исправленный оригинал
#   4. Восстановить дату: touch -r оригинал.backup оригинал


class LLMXMLPipeline:
    def __init__(self, work_dir="tmp"):
        self.work_dir = Path(work_dir)
        self.work_dir.mkdir(exist_ok=True)

    def step1_extract_to_tmp(self, source_path):
        """Шаг 1: Копируем проблемный файл в tmp/"""

        # Генерируем понятный ID для файла
        file_id = hashlib.md5(source_path.encode(), usedforsecurity=False).hexdigest()[:8]

        if "::" in source_path:
            # Файл в архиве
            archive_path, file_in_archive = source_path.split("::")
            tmp_file = self.work_dir / f"llm_{file_id}_{file_in_archive}"

            with zipfile.ZipFile(archive_path, "r") as zip_file:
                with zip_file.open(file_in_archive) as source:
                    with open(tmp_file, "wb") as target:
                        target.write(source.read())
        else:
            # Обычный файл
            source_file = Path(source_path)
            tmp_file = self.work_dir / f"llm_{file_id}_{source_file.name}"
            shutil.copy2(source_file, tmp_file)

        return tmp_file, file_id

    def step2_analyze_and_create_prompt(self, tmp_file, file_id, source_path=None):
        """Шаг 2: Анализ и создание промпта для LLM"""

        # Читаем файл
        with open(tmp_file, "r", encoding="utf-8") as f:
            lines = f.readlines()

        # Анализируем XML
        error_str = ""
        try:
            ET.parse(tmp_file)  # nosec B314 - инструментальный код для анализа локальных файлов
            print("✅ XML валидный - исправление не требуется")
            if source_path:
                print(f"Проверить: python tools/run_diagnostic_tool.py --input {source_path}")
            return None, None
        except ET.ParseError as e:
            error_str = str(e)

        # Извлекаем номер строки из ошибки
        error_line = None
        if "line" in error_str:
            line_match = re.search(r"line (\d+)", error_str)
            if line_match:
                error_line = int(line_match.group(1))

        # Извлекаем ID файла из имени tmp файла для промпта
        archive_file_id = tmp_file.stem.split("_")[-1]  # llm_hash_8357 -> 8357

        # Создаем промпт
        prompt_data = {
            "task": "Исправить сломанный XML в FB2 файле",
            "file_info": {
                "lines_total": len(lines),
                "size_chars": sum(len(line) for line in lines),
                "error": error_str,
                "error_line": error_line,
            },
            "problem_area": self._extract_problem_area(lines, error_line),
            "requirements": {
                "format": "JSON",
                "fields": {
                    "correct_solution": "исправленный XML фрагмент проблемной области",
                    "explanation": "краткое объяснение (максимум 2 предложения)",
                    "changes": ["список конкретных изменений"],
                },
            },
            "instruction": "Ответь ТОЛЬКО в формате JSON, без дополнительных комментариев!",
        }

        # Сохраняем промпт с ID файла в имени
        prompt_file = self.work_dir / f"llm_{file_id}_{archive_file_id}_{archive_file_id}.json"
        with open(prompt_file, "w", encoding="utf-8") as f:
            json.dump(prompt_data, f, ensure_ascii=False, indent=2)

        return prompt_file, error_line

    def _extract_problem_area(self, lines, error_line):
        """Извлекает проблемную область вокруг ошибки"""
        if not error_line:
            return "Не удалось определить проблемную область"

        start = max(0, error_line - 6)
        end = min(len(lines), error_line + 5)

        problem_lines = []
        for i in range(start, end):
            marker = ">>> " if i == error_line - 1 else "    "
            problem_lines.append(f"{marker}{i + 1:3d}: {lines[i].rstrip()}")

        return "\n".join(problem_lines)

    def step3_apply_llm_response(self, tmp_file, file_id, llm_json_response):
        """Шаг 3: Применяем JSON ответ от LLM"""
        print("🤖 Шаг 3: Применение решения от LLM")

        try:
            llm_data = json.loads(llm_json_response)
        except json.JSONDecodeError as e:
            print(f"❌ Ошибка парсинга JSON: {e}")
            return None

        print(f"📝 LLM объяснение: {llm_data.get('explanation', 'Не указано')}")

        # Читаем файл
        with open(tmp_file, "r", encoding="utf-8") as f:
            content = f.read()

        # Применяем исправления (упрощенная версия - заменяем проблемную область)
        # TODO: Сделать более умное применение изменений
        # correct_solution = llm_data.get("correct_solution", "")  # TODO: использовать для исправлений

        # Создаем исправленный файл
        fixed_file = self.work_dir / f"llm_{file_id}_fixed.fb2"

        # Простая замена - найти и заменить проблемную область
        # Это нужно улучшить для универсальности
        with open(fixed_file, "w", encoding="utf-8") as f:
            f.write(content)  # Пока просто копируем, TODO: применить изменения

        print(f"🔧 Исправленный файл создан: {fixed_file}")
        return fixed_file

    def step4_verify_fix(self, fixed_file):
        """Шаг 4: Проверяем результат исправления"""
        print("✅ Шаг 4: Проверка результата")

        try:
            ET.parse(fixed_file)  # nosec B314 - инструментальный код для валидации локальных файлов
            print("✅ Исправленный XML валидный!")
            return True
        except ET.ParseError as e:
            print(f"❌ Исправленный XML все еще содержит ошибки: {e}")
            return False

    def step5_replace_original(self, source_path, fixed_file):
        """Шаг 5: Заменяем содержимое в оригинале"""
        print("🔄 Шаг 5: Замена содержимого в оригинале")

        if "::" in source_path:
            # Файл в архиве
            archive_path, file_in_archive = source_path.split("::")
            return self._replace_in_archive(archive_path, file_in_archive, fixed_file)
        else:
            # Обычный файл
            shutil.copy2(fixed_file, source_path)
            print(f"✅ Файл заменен: {source_path}")
            return True

    def _replace_in_archive(self, archive_path, file_in_archive, fixed_file):
        """Заменяет файл в архиве"""
        # TODO: Реализовать замену в архиве
        print("⚠️ Замена в архиве пока не реализована")
        return False

    def run_pipeline(self, source_path, llm_json_response=None):
        """Запускает полный pipeline"""

        try:
            # Шаг 1: Извлечение
            tmp_file, file_id = self.step1_extract_to_tmp(source_path)

            # Шаг 2: Анализ и промпт
            prompt_file, error_line = self.step2_analyze_and_create_prompt(tmp_file, file_id, source_path)

            if not prompt_file:
                return True

            if not llm_json_response:
                return False

            # Шаг 3: Применение LLM решения
            fixed_file = self.step3_apply_llm_response(tmp_file, file_id, llm_json_response)

            if not fixed_file:
                print("❌ Не удалось применить решение LLM")
                return False

            # Шаг 4: Проверка
            if not self.step4_verify_fix(fixed_file):
                print("❌ Исправление не прошло проверку")
                return False

            # Шаг 5: Замена оригинала
            if self.step5_replace_original(source_path, fixed_file):
                print("🎉 Pipeline успешно завершен!")
                return True
            else:
                print("❌ Не удалось заменить оригинал")
                return False

        except Exception as e:
            print(f"❌ Ошибка в pipeline: {e}")
            return False


def quick_fix_with_json(source_path, llm_json_response):
    """
    🚀 Быстрое исправление файла с готовым JSON ответом от LLM

    Args:
        source_path: Путь к файлу (может быть в архиве)
        llm_json_response: JSON строка с ответом от LLM

    Returns:
        bool: True если успешно исправлено
    """
    pipeline = LLMXMLPipeline()
    return pipeline.run_pipeline(source_path, llm_json_response)


if __name__ == "__main__":
    pipeline = LLMXMLPipeline()

    # Запускаем pipeline
    success = pipeline.run_pipeline(SOURCE_PATH)

    if not success:
        # Показываем только конкретные файлы для работы
        prompt_files = list(Path("tmp").glob("llm_*_*_*.json"))
        fb2_files = list(Path("tmp").glob("llm_*.fb2"))

        print(f"\n� Промпт создан: {prompt_files[-1]}")
        if fb2_files:
            print(f"📁 Файл для исправления: {fb2_files[-1]}")

        # Генерируем инструкцию прямо здесь
        if prompt_files and fb2_files:
            prompt_file = prompt_files[-1]
            fb2_file = fb2_files[-1]

            # Читаем информацию об ошибке
            try:
                with open(prompt_file, "r", encoding="utf-8") as f:
                    prompt_data = json.load(f)
                error_info = prompt_data.get("file_info", {}).get("error", "XML ошибка")
                error_line = prompt_data.get("file_info", {}).get("error_line", "неизвестно")
            except Exception:
                error_info = "XML ошибка"
                error_line = "неизвестно"

            # Извлекаем file_id для дальнейших команд
            file_id = fb2_file.stem.split("_")[1]  # llm_XXXXX_8357.fb2 -> XXXXX
            archive_file_id = fb2_file.stem.split("_")[2]  # llm_XXXXX_8357.fb2 -> 8357

            # Выводим ТОЛЬКО инструкцию
            print(f"""
# ЗАДАЧА: Примени bash исправления к {fb2_file}

## КОНТЕКСТ:
- **Проблемный файл:** `{fb2_file}`
- **Ошибка:** {error_info} (строка {error_line})

## ЧТО МНЕ ДЕЛАТЬ:
1. Применить bash команды из JSON (в конце сообщения) к `{fb2_file}`
2. Проверить валидность XML (оптимально: `xmllint --noout {fb2_file}`)
3. Проверить результат: python tools/run_diagnostic_tool.py --input {fb2_file}
4. Показать количество глав и отсутствие аномалий
5. ЕСЛИ глав больше 1, запустить: python tools/llm_xml_update_orig.py {file_id}
6. Проверить оригинал: python tools/run_diagnostic_tool.py --input {SOURCE_PATH}
7. После успешной проверки оригинала удалить временные файлы: rm tmp/llm_{file_id}_{archive_file_id}.*

---
JSON ОТ LLM ЗДЕСЬ ↓""")
    else:
        print("\n🎉 Файл не требует исправления!")
